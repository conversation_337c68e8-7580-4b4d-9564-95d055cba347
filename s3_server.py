import boto3
import pandas as pd
from botocore.exceptions import NoCredentialsError
from fastapi import FastAPI, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Head<PERSON>
from fastapi.responses import StreamingResponse, RedirectResponse
from pydantic import BaseModel
import base64

bucket_name = 'curionai-demo-rag'
folder_name = 'cic'
region_name = 'ap-east-1'
aws_access_key_id = '********************'
aws_secret_access_key = 'M2ThLrpfz3wYHXTsqMnK429/u7/chuzPDYJcZoGE'

s3_client = boto3.client(
        service_name='s3',
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        region_name=region_name
    )

def get_file_by_s3key(s3key):
    try:
        file_obj = s3_client.get_object(Bucket=bucket_name, Key=s3key)
        file_data = file_obj['Body'].read()
        return {'file_data': file_data, 's3key': s3key}
    except NoCredentialsError:
        print("No AWS credentials found")
        return None


app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/file/")
async def read_file_by_path(path: str, authentication: str = Header(None)):
    if authentication == "secret":
        file_result = get_file_by_s3key(path)
        file_data = file_result['file_data']
        s3key = file_result['s3key']
        filename = s3key.split('/')[-1]
        media_type = ''
        if s3key.endswith('.pdf'):
            media_type='application/pdf'
        else:
            media_type='application/octet-stream'
        response = Response(content=file_data, media_type=media_type)
        response.headers["Content-Disposition"] = f"attachment; filename={filename}"
        response.headers["Access-Control-Expose-Headers"] = "Content-Disposition"
        return response
    else: 
        return RedirectResponse(url="http://localhost:8001/login.html")


#http://localhost:8001/test.html?etag=65ddfe3f819dd260f9ee0dce8d209d2a-2
#http://localhost:8001/test.html?etag=72432c9fb76cf45094c29fc92c4b16f0