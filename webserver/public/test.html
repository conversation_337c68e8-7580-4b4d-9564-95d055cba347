<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>API Call on Page Load</title>
    <style>
      #loading {
        display: none;
      }
    </style>
    <script>
      function handleFileAccess(url) {
        document.getElementById("loading").style.display = "block";
        fetch(url, {
          headers: {
            Authentication: localStorage.getItem("jwt"),
          },
        })
          .then((response) => {
            document.getElementById("loading").style.display = "none";
            if (response.redirected) {
              window.open(response.url, "_blank");
            } else if (response.ok) {
              const contentDisposition = response.headers.get(
                "Content-Disposition"
              );
              const filename = contentDisposition.split("filename=")[1];
              const isPdf = filename.endsWith(".pdf");
              if (isPdf) {
                return response.blob();
              } else {
                return response.blob().then((blob) => {
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement("a");
                  link.href = url;
                  link.download = filename;
                  link.click();
                });
              }
            } else {
              throw new Error("API response error");
            }
          })
          .then((blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              window.open(url, "_blank");
            }
          })
          .catch((error) => {
            document.getElementById("loading").style.display = "none";
            console.error("Error:", error);
          });
      }
    </script>
  </head>
  <body>
    <h1>Imagine This is the Chat UI</h1>
    <a
      href="http://localhost:8000/file/?path=cic%2FSUG2018e.pdf"
      id="test-link"
      onclick="handleFileAccess(this.href); return false;"
      >File link</a
    >
    <div id="loading">Loading file please wait...</div>
  </body>
</html>
