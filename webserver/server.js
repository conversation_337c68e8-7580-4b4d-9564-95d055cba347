const express = require("express");
const cors = require("cors");
const path = require("path");

const app = express();
const port = 8001;

// Enable CORS
app.use(cors());

// Serve static files from the 'public' directory
app.use(express.static(path.join(__dirname, "public")));

// Define a route for the root path
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "test.html"));
});

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});
